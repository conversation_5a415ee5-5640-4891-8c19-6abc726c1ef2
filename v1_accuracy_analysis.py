"""
v1.txt振动预测准确率分析
基于v1_improved_integrated_lstm_kalman_results.csv计算详细的准确率指标
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy import signal
import warnings

warnings.filterwarnings("ignore")

# 设置中文字体
try:
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['font.family'] = 'sans-serif'
    print("中文字体设置成功: SimHei")
except:
    plt.rcParams['axes.unicode_minus'] = False
    print("使用默认字体")

def calculate_accuracy_metrics(y_true, y_pred, method_name="预测方法"):
    """
    计算详细的准确率指标
    
    参数:
    - y_true: 真实值
    - y_pred: 预测值
    - method_name: 方法名称
    
    返回:
    - metrics: 包含各种准确率指标的字典
    """
    # 基本误差指标
    mae = np.mean(np.abs(y_true - y_pred))
    mse = np.mean((y_true - y_pred) ** 2)
    rmse = np.sqrt(mse)
    
    # 相对误差指标
    # 避免除零错误
    non_zero_mask = np.abs(y_true) > 1e-15
    if np.sum(non_zero_mask) > 0:
        mape = np.mean(np.abs((y_true[non_zero_mask] - y_pred[non_zero_mask]) / y_true[non_zero_mask])) * 100
    else:
        mape = 0.0
    
    # 相关性指标
    correlation = np.corrcoef(y_true, y_pred)[0, 1] if len(y_true) > 1 else 1.0
    
    # R²决定系数
    ss_res = np.sum((y_true - y_pred) ** 2)
    ss_tot = np.sum((y_true - np.mean(y_true)) ** 2)
    r2_score = 1 - (ss_res / ss_tot) if ss_tot != 0 else 1.0
    
    # 标准化RMSE
    data_range = np.max(y_true) - np.min(y_true)
    nrmse = (rmse / data_range * 100) if data_range != 0 else 0.0
    
    # 最大误差
    max_error = np.max(np.abs(y_true - y_pred))
    
    # 偏差（bias）
    bias = np.mean(y_pred - y_true)
    
    # 准确率（基于相对误差阈值）
    if np.sum(non_zero_mask) > 0:
        relative_errors = np.abs((y_true[non_zero_mask] - y_pred[non_zero_mask]) / y_true[non_zero_mask]) * 100
        accuracy_1percent = np.sum(relative_errors <= 1.0) / len(relative_errors) * 100
        accuracy_5percent = np.sum(relative_errors <= 5.0) / len(relative_errors) * 100
        accuracy_10percent = np.sum(relative_errors <= 10.0) / len(relative_errors) * 100
    else:
        accuracy_1percent = 100.0
        accuracy_5percent = 100.0
        accuracy_10percent = 100.0
    
    metrics = {
        'method_name': method_name,
        'mae': mae,
        'mse': mse,
        'rmse': rmse,
        'mape': mape,
        'r2_score': r2_score,
        'correlation': correlation,
        'max_error': max_error,
        'accuracy_1percent': accuracy_1percent,
        'accuracy_5percent': accuracy_5percent,
        'accuracy_10percent': accuracy_10percent,
        'nrmse': nrmse,
        'bias': bias,
        'data_points': len(y_true)
    }
    
    return metrics

def print_accuracy_report(lstm_metrics, kalman_metrics):
    """打印准确率报告"""
    print("\n" + "="*80)
    print("v1.txt振动预测准确率详细分析报告")
    print("="*80)
    
    print(f"\n📊 数据集信息:")
    print(f"  测试数据点数: {lstm_metrics['data_points']}")
    print(f"  数据来源: v1.txt (从vib1.txt均匀采样的2000个点)")
    
    print(f"\n🎯 基本误差指标:")
    print(f"  平均绝对误差 (MAE):")
    print(f"    LSTM预测: {lstm_metrics['mae']:.6f} μm")
    print(f"    集成LSTM-卡尔曼: {kalman_metrics['mae']:.6f} μm")
    mae_improvement = (lstm_metrics['mae'] - kalman_metrics['mae']) / lstm_metrics['mae'] * 100 if lstm_metrics['mae'] != 0 else 0
    print(f"    改善程度: {mae_improvement:.2f}%")

    print(f"\n  均方根误差 (RMSE):")
    print(f"    LSTM预测: {lstm_metrics['rmse']:.6f} μm")
    print(f"    集成LSTM-卡尔曼: {kalman_metrics['rmse']:.6f} μm")
    rmse_improvement = (lstm_metrics['rmse'] - kalman_metrics['rmse']) / lstm_metrics['rmse'] * 100 if lstm_metrics['rmse'] != 0 else 0
    print(f"    改善程度: {rmse_improvement:.2f}%")
    
    print(f"\n  平均绝对百分比误差 (MAPE):")
    print(f"    LSTM预测: {lstm_metrics['mape']:.2f}%")
    print(f"    集成LSTM-卡尔曼: {kalman_metrics['mape']:.2f}%")
    mape_improvement = (lstm_metrics['mape'] - kalman_metrics['mape']) / lstm_metrics['mape'] * 100 if lstm_metrics['mape'] != 0 else 0
    print(f"    改善程度: {mape_improvement:.2f}%")
    
    print(f"\n📈 相关性指标:")
    print(f"  决定系数 (R²):")
    print(f"    LSTM预测: {lstm_metrics['r2_score']:.6f}")
    print(f"    集成LSTM-卡尔曼: {kalman_metrics['r2_score']:.6f}")
    print(f"    改善: +{kalman_metrics['r2_score'] - lstm_metrics['r2_score']:.6f}")
    
    print(f"\n  相关系数:")
    print(f"    LSTM预测: {lstm_metrics['correlation']:.6f}")
    print(f"    集成LSTM-卡尔曼: {kalman_metrics['correlation']:.6f}")
    print(f"    改善: +{kalman_metrics['correlation'] - lstm_metrics['correlation']:.6f}")
    
    print(f"\n🎯 准确率指标 (基于相对误差阈值):")
    print(f"  ±1%以内准确率:")
    print(f"    LSTM预测: {lstm_metrics['accuracy_1percent']:.2f}%")
    print(f"    集成LSTM-卡尔曼: {kalman_metrics['accuracy_1percent']:.2f}%")
    print(f"    改善: +{kalman_metrics['accuracy_1percent'] - lstm_metrics['accuracy_1percent']:.2f}%")
    
    print(f"\n  ±5%以内准确率:")
    print(f"    LSTM预测: {lstm_metrics['accuracy_5percent']:.2f}%")
    print(f"    集成LSTM-卡尔曼: {kalman_metrics['accuracy_5percent']:.2f}%")
    print(f"    改善: +{kalman_metrics['accuracy_5percent'] - lstm_metrics['accuracy_5percent']:.2f}%")
    
    print(f"\n  ±10%以内准确率:")
    print(f"    LSTM预测: {lstm_metrics['accuracy_10percent']:.2f}%")
    print(f"    集成LSTM-卡尔曼: {kalman_metrics['accuracy_10percent']:.2f}%")
    print(f"    改善: +{kalman_metrics['accuracy_10percent'] - lstm_metrics['accuracy_10percent']:.2f}%")
    
    print(f"\n🔍 其他指标:")
    print(f"  最大误差:")
    print(f"    LSTM预测: {lstm_metrics['max_error']:.6f} μm")
    print(f"    集成LSTM-卡尔曼: {kalman_metrics['max_error']:.6f} μm")
    max_error_improvement = (lstm_metrics['max_error'] - kalman_metrics['max_error']) / lstm_metrics['max_error'] * 100 if lstm_metrics['max_error'] != 0 else 0
    print(f"    改善程度: {max_error_improvement:.2f}%")
    
    print(f"\n  标准化RMSE:")
    print(f"    LSTM预测: {lstm_metrics['nrmse']:.3f}%")
    print(f"    集成LSTM-卡尔曼: {kalman_metrics['nrmse']:.3f}%")
    nrmse_improvement = (lstm_metrics['nrmse'] - kalman_metrics['nrmse']) / lstm_metrics['nrmse'] * 100 if lstm_metrics['nrmse'] != 0 else 0
    print(f"    改善程度: {nrmse_improvement:.2f}%")

def visualize_accuracy_analysis(data):
    """可视化准确率分析"""
    # 提取数据
    real_values = data['real_displacement'].values
    lstm_predictions = data['lstm_prediction'].values
    kalman_predictions = data['improved_integrated_kalman_prediction'].values
    
    # 计算误差
    lstm_errors = np.abs(real_values - lstm_predictions)
    kalman_errors = np.abs(real_values - kalman_predictions)
    
    # 创建图形
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('v1.txt振动预测准确率分析', fontsize=16, fontweight='bold')
    
    # 1. 绝对误差对比
    axes[0, 0].plot(lstm_errors, 'b-', alpha=0.7, linewidth=1, label='LSTM预测误差')
    axes[0, 0].plot(kalman_errors, 'r-', alpha=0.7, linewidth=1, label='集成LSTM-卡尔曼误差')
    axes[0, 0].set_xlabel('数据点')
    axes[0, 0].set_ylabel('绝对误差 (μm)')
    axes[0, 0].set_title('绝对误差对比')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. 相对误差对比（百分比）
    non_zero_mask = np.abs(real_values) > 1e-15
    if np.sum(non_zero_mask) > 0:
        lstm_rel_errors = np.abs((real_values[non_zero_mask] - lstm_predictions[non_zero_mask]) / real_values[non_zero_mask]) * 100
        kalman_rel_errors = np.abs((real_values[non_zero_mask] - kalman_predictions[non_zero_mask]) / real_values[non_zero_mask]) * 100
        
        axes[0, 1].plot(lstm_rel_errors, 'b-', alpha=0.7, linewidth=1, label='LSTM相对误差')
        axes[0, 1].plot(kalman_rel_errors, 'r-', alpha=0.7, linewidth=1, label='集成LSTM-卡尔曼相对误差')
        axes[0, 1].set_xlabel('数据点')
        axes[0, 1].set_ylabel('相对误差 (%)')
        axes[0, 1].set_title('相对误差对比')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
    
    # 3. 误差分布直方图
    axes[0, 2].hist(lstm_errors, bins=30, alpha=0.7, color='blue', label='LSTM误差分布', density=True)
    axes[0, 2].hist(kalman_errors, bins=30, alpha=0.7, color='red', label='集成LSTM-卡尔曼误差分布', density=True)
    axes[0, 2].set_xlabel('绝对误差 (μm)')
    axes[0, 2].set_ylabel('密度')
    axes[0, 2].set_title('误差分布对比')
    axes[0, 2].legend()
    axes[0, 2].grid(True, alpha=0.3)
    
    # 4. LSTM预测散点图
    axes[1, 0].scatter(real_values, lstm_predictions, alpha=0.6, s=10, color='blue')
    min_val, max_val = min(real_values.min(), lstm_predictions.min()), max(real_values.max(), lstm_predictions.max())
    axes[1, 0].plot([min_val, max_val], [min_val, max_val], 'k--', alpha=0.8, linewidth=2)
    axes[1, 0].set_xlabel('真实值 (μm)')
    axes[1, 0].set_ylabel('LSTM预测值 (μm)')
    axes[1, 0].set_title('LSTM预测 vs 真实值')
    axes[1, 0].grid(True, alpha=0.3)

    # 5. 集成方法预测散点图
    axes[1, 1].scatter(real_values, kalman_predictions, alpha=0.6, s=10, color='red')
    axes[1, 1].plot([min_val, max_val], [min_val, max_val], 'k--', alpha=0.8, linewidth=2)
    axes[1, 1].set_xlabel('真实值 (μm)')
    axes[1, 1].set_ylabel('集成LSTM-卡尔曼预测值 (μm)')
    axes[1, 1].set_title('集成LSTM-卡尔曼预测 vs 真实值')
    axes[1, 1].grid(True, alpha=0.3)
    
    # 6. 累积准确率曲线
    if np.sum(non_zero_mask) > 0:
        thresholds = np.linspace(0, 20, 100)
        lstm_accuracies = []
        kalman_accuracies = []
        
        for threshold in thresholds:
            lstm_acc = np.sum(lstm_rel_errors <= threshold) / len(lstm_rel_errors) * 100
            kalman_acc = np.sum(kalman_rel_errors <= threshold) / len(kalman_rel_errors) * 100
            lstm_accuracies.append(lstm_acc)
            kalman_accuracies.append(kalman_acc)
        
        axes[1, 2].plot(thresholds, lstm_accuracies, 'b-', linewidth=2, label='LSTM预测')
        axes[1, 2].plot(thresholds, kalman_accuracies, 'r-', linewidth=2, label='集成LSTM-卡尔曼')
        axes[1, 2].axvline(x=1, color='gray', linestyle='--', alpha=0.7, label='±1%阈值')
        axes[1, 2].axvline(x=5, color='gray', linestyle='--', alpha=0.7, label='±5%阈值')
        axes[1, 2].set_xlabel('误差阈值 (%)')
        axes[1, 2].set_ylabel('累积准确率 (%)')
        axes[1, 2].set_title('累积准确率曲线')
        axes[1, 2].legend()
        axes[1, 2].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('v1_accuracy_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("准确率分析图表已保存到: v1_accuracy_analysis.png")

def main():
    """主函数"""
    print("="*60)
    print("v1.txt振动预测准确率分析")
    print("="*60)
    
    try:
        # 读取预测结果
        print("正在读取预测结果文件...")
        data = pd.read_csv('v1_improved_integrated_lstm_kalman_results.csv')
        print(f"成功读取 {len(data)} 个预测结果")
        
        # 提取数据
        real_values = data['real_displacement'].values
        lstm_predictions = data['lstm_prediction'].values
        kalman_predictions = data['improved_integrated_kalman_prediction'].values
        
        print(f"\n数据范围:")
        print(f"  真实值: {real_values.min():.6f} ~ {real_values.max():.6f} μm")
        print(f"  LSTM预测: {lstm_predictions.min():.6f} ~ {lstm_predictions.max():.6f} μm")
        print(f"  集成预测: {kalman_predictions.min():.6f} ~ {kalman_predictions.max():.6f} μm")
        
        # 计算准确率指标
        print("\n正在计算准确率指标...")
        lstm_metrics = calculate_accuracy_metrics(real_values, lstm_predictions, "LSTM预测")
        kalman_metrics = calculate_accuracy_metrics(real_values, kalman_predictions, "集成LSTM-卡尔曼")
        
        # 打印报告
        print_accuracy_report(lstm_metrics, kalman_metrics)
        
        # 保存指标到CSV
        metrics_df = pd.DataFrame([lstm_metrics, kalman_metrics])
        metrics_df.to_csv('v1_accuracy_metrics.csv', index=False)
        print(f"\n准确率指标已保存到: v1_accuracy_metrics.csv")
        
        # 生成可视化
        print("\n正在生成准确率分析图表...")
        visualize_accuracy_analysis(data)
        
        print("\n" + "="*60)
        print("v1.txt振动预测准确率分析完成！")
        print("="*60)
        
    except FileNotFoundError:
        print("❌ 错误: 未找到 v1_improved_integrated_lstm_kalman_results.csv 文件")
        print("请先运行 improve.py 生成预测结果")
    except Exception as e:
        print(f"❌ 分析过程中出错: {e}")

if __name__ == "__main__":
    main()
