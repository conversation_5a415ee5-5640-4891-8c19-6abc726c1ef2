"""
实时数据流处理器
支持多种数据源的实时振动预测系统
"""

import asyncio
import queue
import threading
import time
import numpy as np
import pandas as pd
from collections import deque
import torch
import torch.nn as nn
from sklearn.preprocessing import MinMaxScaler
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation
import warnings

warnings.filterwarnings("ignore")

class RealTimeStreamProcessor:
    """实时数据流处理器"""
    
    def __init__(self, model, scaler, window_size=25, buffer_size=10000):
        self.model = model
        self.scaler = scaler
        self.window_size = window_size
        self.model.eval()
        
        # 数据缓冲区
        self.raw_buffer = queue.Queue(maxsize=buffer_size)
        self.processed_buffer = deque(maxlen=window_size)
        
        # 预测结果存储
        self.predictions = deque(maxlen=1000)
        self.timestamps = deque(maxlen=1000)
        self.real_values = deque(maxlen=1000)
        
        # 控制标志
        self.is_running = False
        self.is_initialized = False
        
        # 性能统计
        self.processing_times = deque(maxlen=100)
        self.prediction_count = 0
        self.dropped_count = 0
        
        # 数据质量监控
        self.outlier_threshold = 5.0  # 放宽到5倍标准差
        self.last_values = deque(maxlen=20)  # 增加历史窗口
        self.enable_outlier_detection = True  # 可以关闭异常检测
        
    def initialize_with_historical_data(self, historical_data):
        """使用历史数据初始化处理器"""
        if len(historical_data) < self.window_size:
            raise ValueError(f"历史数据长度必须至少为 {self.window_size}")
        
        # 标准化历史数据
        normalized_data = self.scaler.transform(
            np.array(historical_data[-self.window_size:]).reshape(-1, 1)
        ).flatten()
        
        # 填充处理缓冲区
        self.processed_buffer.clear()
        for value in normalized_data:
            self.processed_buffer.append(value)
        
        self.is_initialized = True
        print(f"实时处理器已初始化，包含 {len(self.processed_buffer)} 个历史数据点")
    
    def add_raw_data(self, value, timestamp=None):
        """添加原始数据到缓冲区"""
        if timestamp is None:
            timestamp = time.time()
        
        data_point = {
            'value': value,
            'timestamp': timestamp,
            'sequence': self.prediction_count
        }
        
        try:
            self.raw_buffer.put_nowait(data_point)
        except queue.Full:
            self.dropped_count += 1
            print(f"警告: 数据缓冲区满，丢弃数据点。总丢弃: {self.dropped_count}")
    
    def _is_outlier(self, value):
        """检测异常值"""
        if not self.enable_outlier_detection or len(self.last_values) < 5:
            return False

        recent_values = list(self.last_values)
        mean_val = np.mean(recent_values)
        std_val = np.std(recent_values)

        if std_val == 0:
            return False

        z_score = abs((value - mean_val) / std_val)
        return z_score > self.outlier_threshold
    
    def _preprocess_data(self, raw_value):
        """数据预处理"""
        # 1. 异常值检测和处理
        if self._is_outlier(raw_value):
            # 使用更温和的处理方式：线性插值而不是直接替换
            if len(self.last_values) >= 2:
                # 使用最近两个值的平均值进行平滑
                recent_values = list(self.last_values)[-2:]
                processed_value = np.mean(recent_values)
                print(f"检测到异常值 {raw_value:.6f}，平滑为 {processed_value:.6f}")
            else:
                processed_value = raw_value
        else:
            processed_value = raw_value

        # 2. 更新最近值记录（使用原始值而不是处理后的值）
        self.last_values.append(raw_value)

        # 3. 数据标准化
        normalized_value = self.scaler.transform([[processed_value]])[0, 0]

        return normalized_value
    
    def _make_prediction(self):
        """进行LSTM预测"""
        if len(self.processed_buffer) < self.window_size:
            return None
        
        start_time = time.time()
        
        # 准备输入序列
        input_seq = torch.FloatTensor(list(self.processed_buffer)).unsqueeze(0).unsqueeze(-1)
        
        # LSTM预测
        with torch.no_grad():
            normalized_prediction = self.model(input_seq).item()
        
        # 反标准化
        prediction = self.scaler.inverse_transform([[normalized_prediction]])[0, 0]
        
        # 记录处理时间
        processing_time = time.time() - start_time
        self.processing_times.append(processing_time)
        
        return prediction
    
    def process_single_point(self, raw_value, timestamp=None):
        """处理单个数据点"""
        if not self.is_initialized:
            raise RuntimeError("处理器未初始化，请先调用 initialize_with_historical_data()")
        
        if timestamp is None:
            timestamp = time.time()
        
        # 1. 数据预处理
        processed_value = self._preprocess_data(raw_value)
        
        # 2. 更新滑动窗口
        self.processed_buffer.append(processed_value)
        
        # 3. 进行预测
        prediction = self._make_prediction()
        
        # 4. 存储结果
        if prediction is not None:
            self.predictions.append(prediction)
            self.timestamps.append(timestamp)
            self.real_values.append(raw_value)
            self.prediction_count += 1
        
        return prediction
    
    async def start_real_time_processing(self, data_source, prediction_callback=None):
        """启动实时处理"""
        self.is_running = True
        print("开始实时数据流处理...")
        
        while self.is_running:
            try:
                # 从数据源读取数据
                raw_data = await data_source.read_data()
                
                if raw_data is not None:
                    # 处理数据点
                    prediction = self.process_single_point(raw_data)
                    
                    # 调用回调函数
                    if prediction is not None and prediction_callback:
                        await prediction_callback(raw_data, prediction, time.time())
                
                # 控制处理频率
                await asyncio.sleep(0.001)  # 1ms间隔，支持1000Hz采样率
                
            except Exception as e:
                print(f"处理错误: {e}")
                await asyncio.sleep(0.1)
    
    def stop_processing(self):
        """停止实时处理"""
        self.is_running = False
        print("实时处理已停止")
    
    def get_performance_stats(self):
        """获取性能统计"""
        if len(self.processing_times) == 0:
            return {}
        
        return {
            'avg_processing_time': np.mean(self.processing_times),
            'max_processing_time': np.max(self.processing_times),
            'min_processing_time': np.min(self.processing_times),
            'processing_rate': 1.0 / np.mean(self.processing_times),
            'total_predictions': self.prediction_count,
            'dropped_data_points': self.dropped_count,
            'buffer_utilization': len(self.processed_buffer) / self.window_size
        }
    
    def get_recent_results(self, n=100):
        """获取最近的预测结果"""
        n = min(n, len(self.predictions))
        return {
            'timestamps': list(self.timestamps)[-n:],
            'real_values': list(self.real_values)[-n:],
            'predictions': list(self.predictions)[-n:]
        }

# 模拟数据源（用于测试）
class SimulatedDataSource:
    """模拟实时数据源"""
    
    def __init__(self, data_file='v1.txt', sampling_rate=100):
        # 加载测试数据
        self.data = pd.read_csv(data_file, sep='\t', encoding='utf-8')
        self.displacement_data = self.data.iloc[:, 1].values  # 位移列
        self.current_index = 0
        self.sampling_interval = 1.0 / sampling_rate
        self.last_time = time.time()
        
    async def read_data(self):
        """模拟实时数据读取"""
        current_time = time.time()
        
        # 控制采样率
        if current_time - self.last_time < self.sampling_interval:
            return None
        
        if self.current_index >= len(self.displacement_data):
            return None
        
        value = self.displacement_data[self.current_index]
        self.current_index += 1
        self.last_time = current_time
        
        # 添加一些随机噪声模拟真实传感器
        noise = np.random.normal(0, 0.001)
        return value + noise
    
    def is_connected(self):
        return self.current_index < len(self.displacement_data)

# 实时可视化
class RealTimeVisualizer:
    """实时数据可视化"""
    
    def __init__(self, processor, max_points=200):
        self.processor = processor
        self.max_points = max_points
        
        # 创建图形
        self.fig, (self.ax1, self.ax2) = plt.subplots(2, 1, figsize=(12, 8))
        self.fig.suptitle('实时振动预测监控')
        
        # 初始化空线条
        self.line_real, = self.ax1.plot([], [], 'b-', label='真实值', alpha=0.7)
        self.line_pred, = self.ax1.plot([], [], 'r-', label='预测值', alpha=0.8)
        self.ax1.set_ylabel('位移 (μm)')
        self.ax1.set_title('实时预测对比')
        self.ax1.legend()
        self.ax1.grid(True, alpha=0.3)
        
        # 误差图
        self.line_error, = self.ax2.plot([], [], 'g-', label='预测误差', alpha=0.8)
        self.ax2.set_xlabel('时间步')
        self.ax2.set_ylabel('误差 (μm)')
        self.ax2.set_title('预测误差')
        self.ax2.legend()
        self.ax2.grid(True, alpha=0.3)
        
    def update_plot(self, frame):
        """更新图形"""
        results = self.processor.get_recent_results(self.max_points)
        
        if len(results['real_values']) > 1:
            # 更新数据
            x_data = range(len(results['real_values']))
            
            self.line_real.set_data(x_data, results['real_values'])
            self.line_pred.set_data(x_data, results['predictions'])
            
            # 计算误差
            errors = [abs(r - p) for r, p in zip(results['real_values'], results['predictions'])]
            self.line_error.set_data(x_data, errors)
            
            # 自动调整坐标轴
            for ax in [self.ax1, self.ax2]:
                ax.relim()
                ax.autoscale_view()
        
        return self.line_real, self.line_pred, self.line_error
    
    def start_animation(self, interval=100):
        """启动动画"""
        self.animation = FuncAnimation(
            self.fig, self.update_plot, interval=interval, blit=True
        )
        plt.show()

# 使用示例
async def prediction_callback(real_value, prediction, timestamp):
    """预测结果回调函数"""
    error = abs(real_value - prediction)
    print(f"时间: {timestamp:.3f}, 真实: {real_value:.6f}, 预测: {prediction:.6f}, 误差: {error:.6f}")

if __name__ == "__main__":
    print("实时数据流处理器演示")
    print("注意: 本演示需要v1.txt数据文件")
    print("程序将自动训练LSTM模型并进行实时预测演示")
