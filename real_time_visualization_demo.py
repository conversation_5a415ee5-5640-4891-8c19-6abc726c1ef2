"""
实时数据流处理可视化演示
生成静态图表展示实时处理效果
"""

import asyncio
import time
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
from sklearn.preprocessing import MinMaxScaler
import matplotlib.pyplot as plt
from real_time_stream_processor import RealTimeStreamProcessor, SimulatedDataSource
import warnings

warnings.filterwarnings("ignore")

# 设置中文字体
try:
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['font.family'] = 'sans-serif'
    print("中文字体设置成功: SimHei")
except:
    plt.rcParams['axes.unicode_minus'] = False
    print("使用默认字体")

# 简化的LSTM模型
class DisplacementLSTM(nn.Module):
    def __init__(self, input_size=1, hidden_size=64, output_size=1):
        super().__init__()
        self.hidden_size = hidden_size
        self.lstm = nn.LSTM(input_size, hidden_size, batch_first=True)
        self.linear = nn.Linear(hidden_size, output_size)
        self.dropout = nn.Dropout(0.1)

    def forward(self, x):
        lstm_out, _ = self.lstm(x)
        out = lstm_out[:, -1, :]
        out = self.dropout(out)
        prediction = self.linear(out)
        return prediction

def create_sequences(data, window_size):
    """创建训练序列"""
    sequences = []
    targets = []
    
    for i in range(len(data) - window_size):
        seq = data[i:i+window_size]
        target = data[i+window_size]
        sequences.append(seq)
        targets.append(target)
    
    return np.array(sequences), np.array(targets)

def train_model_for_visualization():
    """训练模型用于可视化演示"""
    print("正在训练LSTM模型...")

    # 1. 加载训练数据
    try:
        data = pd.read_csv('v1.txt', sep='\t', encoding='utf-8')
        displacement_data = data.iloc[:, 1].values  # 位移列
        print(f"加载数据成功，总数据点数: {len(displacement_data)}")

        # 使用前1000个点作为训练数据
        train_data = displacement_data[:1000]
        print(f"使用前1000个点进行模型训练")

    except FileNotFoundError:
        print("错误: 未找到v1.txt文件")
        return None, None

    # 2. 数据预处理（只用训练数据拟合scaler）
    scaler = MinMaxScaler(feature_range=(-1, 1))
    train_normalized = scaler.fit_transform(train_data.reshape(-1, 1))
    train_normalized = torch.FloatTensor(train_normalized.flatten())
    
    # 3. 创建训练序列（使用训练数据）
    window_size = 25
    X, y = create_sequences(train_normalized.numpy(), window_size)

    X = torch.FloatTensor(X).unsqueeze(-1)
    y = torch.FloatTensor(y)

    print(f"训练序列数量: {len(X)}, 窗口大小: {window_size}")
    
    # 4. 训练模型
    model = DisplacementLSTM(input_size=1, hidden_size=96, output_size=1)
    criterion = nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=0.008)

    epochs = 100  # 增加训练轮次以提高精度
    print(f"开始训练，共{epochs}个epoch...")
    for epoch in range(epochs):
        optimizer.zero_grad()
        outputs = model(X).squeeze()
        loss = criterion(outputs, y)
        loss.backward()
        optimizer.step()

        if epoch % 25 == 0:
            print(f'Epoch {epoch:3d}: Loss = {loss.item():.6f}')

    model.eval()
    print(f"模型训练完成！最终损失: {loss.item():.6f}")

    return model, scaler

async def collect_real_time_data(processor, data_source, num_points=200):
    """收集实时处理数据"""
    print(f"开始收集 {num_points} 个实时处理数据点...")

    real_values = []
    predictions = []
    timestamps = []
    processing_times = []

    successful_predictions = 0

    for i in range(num_points):
        # 模拟实时数据读取
        raw_data = await data_source.read_data()

        if raw_data is not None:
            start_time = time.time()
            prediction = processor.process_single_point(raw_data)
            processing_time = time.time() - start_time

            if prediction is not None:
                real_values.append(raw_data)
                predictions.append(prediction)
                timestamps.append(time.time())
                processing_times.append(processing_time)
                successful_predictions += 1

        # 控制采集频率
        await asyncio.sleep(0.005)  # 200Hz采集，加快演示速度

        # 更频繁的进度报告
        if (i + 1) % 100 == 0:
            print(f"已处理 {i+1}/{num_points} 个数据点，成功预测 {successful_predictions} 个")

    print(f"数据收集完成！总共成功预测 {successful_predictions} 个数据点")

    return {
        'real_values': real_values,
        'predictions': predictions,
        'timestamps': timestamps,
        'processing_times': processing_times
    }

def create_real_time_visualization(data):
    """创建实时处理可视化图表"""
    print("生成实时处理可视化图表...")
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('实时数据流处理可视化演示', fontsize=16, fontweight='bold')
    
    real_values = np.array(data['real_values'])
    predictions = np.array(data['predictions'])
    processing_times = np.array(data['processing_times'])
    time_steps = range(len(real_values))
    
    # 计算误差
    errors = np.abs(real_values - predictions)
    
    # 第一张图：实时预测对比
    axes[0, 0].plot(time_steps, real_values, 'b-', linewidth=2, label='真实位移', alpha=0.8)
    axes[0, 0].plot(time_steps, predictions, 'r--', linewidth=2, label='LSTM+AKF预测', alpha=0.8)
    axes[0, 0].set_xlabel('时间步')
    axes[0, 0].set_ylabel('位移 (μm)')
    axes[0, 0].set_title('实时预测对比')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 添加统计信息
    mae = np.mean(errors)
    rmse = np.sqrt(np.mean(errors**2))
    axes[0, 0].text(0.02, 0.98, f'MAE: {mae:.6f}μm\nRMSE: {rmse:.6f}μm', 
                    transform=axes[0, 0].transAxes, verticalalignment='top',
                    bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    # 第二张图：预测误差
    axes[0, 1].plot(time_steps, errors, 'g-', linewidth=2, label='预测误差', alpha=0.8)
    axes[0, 1].axhline(y=np.mean(errors), color='orange', linestyle='--', 
                       label=f'平均误差: {np.mean(errors):.6f}μm')
    axes[0, 1].set_xlabel('时间步')
    axes[0, 1].set_ylabel('绝对误差 (μm)')
    axes[0, 1].set_title('实时预测误差')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # 第三张图：处理时间分析
    axes[1, 0].plot(time_steps, processing_times * 1000, 'purple', linewidth=2, 
                    label='处理时间', alpha=0.8)
    axes[1, 0].axhline(y=np.mean(processing_times) * 1000, color='red', linestyle='--',
                       label=f'平均时间: {np.mean(processing_times)*1000:.2f}ms')
    axes[1, 0].set_xlabel('时间步')
    axes[1, 0].set_ylabel('处理时间 (ms)')
    axes[1, 0].set_title('实时处理性能')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # 添加性能统计
    avg_time = np.mean(processing_times) * 1000
    max_time = np.max(processing_times) * 1000
    processing_rate = 1.0 / np.mean(processing_times)
    axes[1, 0].text(0.02, 0.98, f'平均: {avg_time:.2f}ms\n最大: {max_time:.2f}ms\n速率: {processing_rate:.1f}次/秒', 
                    transform=axes[1, 0].transAxes, verticalalignment='top',
                    bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    
    # 第四张图：误差分布直方图
    axes[1, 1].hist(errors, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
    axes[1, 1].axvline(x=np.mean(errors), color='red', linestyle='--', linewidth=2,
                       label=f'平均误差: {np.mean(errors):.6f}μm')
    axes[1, 1].axvline(x=np.median(errors), color='green', linestyle='--', linewidth=2,
                       label=f'中位数误差: {np.median(errors):.6f}μm')
    axes[1, 1].set_xlabel('预测误差 (μm)')
    axes[1, 1].set_ylabel('频次')
    axes[1, 1].set_title('误差分布直方图')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('real_time_processing_visualization.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("可视化图表已保存到: real_time_processing_visualization.png")

def create_performance_dashboard(data):
    """创建性能仪表板"""
    print("生成性能仪表板...")
    
    real_values = np.array(data['real_values'])
    predictions = np.array(data['predictions'])
    processing_times = np.array(data['processing_times'])
    
    # 计算关键指标
    errors = np.abs(real_values - predictions)
    mae = np.mean(errors)
    rmse = np.sqrt(np.mean(errors**2))
    max_error = np.max(errors)
    min_error = np.min(errors)
    
    avg_processing_time = np.mean(processing_times) * 1000
    max_processing_time = np.max(processing_times) * 1000
    processing_rate = 1.0 / np.mean(processing_times)
    
    # 计算准确率
    relative_errors = np.abs((real_values - predictions) / real_values) * 100
    accuracy_1percent = np.sum(relative_errors <= 1.0) / len(relative_errors) * 100
    accuracy_5percent = np.sum(relative_errors <= 5.0) / len(relative_errors) * 100
    
    # 创建仪表板
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('实时数据流处理性能仪表板', fontsize=18, fontweight='bold')
    
    # 1. 精度指标
    metrics = ['MAE', 'RMSE', '最大误差', '最小误差']
    values = [mae, rmse, max_error, min_error]
    colors = ['skyblue', 'lightgreen', 'salmon', 'gold']
    
    bars = axes[0, 0].bar(metrics, values, color=colors, alpha=0.8)
    axes[0, 0].set_ylabel('误差 (μm)')
    axes[0, 0].set_title('预测精度指标')
    axes[0, 0].grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, value in zip(bars, values):
        axes[0, 0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(values)*0.01,
                        f'{value:.6f}', ha='center', va='bottom', fontweight='bold')
    
    # 2. 性能指标
    perf_metrics = ['平均处理时间\n(ms)', '最大处理时间\n(ms)', '处理速率\n(次/秒)']
    perf_values = [avg_processing_time, max_processing_time, processing_rate]
    perf_colors = ['lightcoral', 'orange', 'lightblue']
    
    bars = axes[0, 1].bar(perf_metrics, perf_values, color=perf_colors, alpha=0.8)
    axes[0, 1].set_ylabel('数值')
    axes[0, 1].set_title('处理性能指标')
    axes[0, 1].grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, value in zip(bars, perf_values):
        axes[0, 1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(perf_values)*0.01,
                        f'{value:.2f}', ha='center', va='bottom', fontweight='bold')
    
    # 3. 准确率指标
    acc_metrics = ['±1%准确率', '±5%准确率']
    acc_values = [accuracy_1percent, accuracy_5percent]
    acc_colors = ['mediumseagreen', 'darkseagreen']
    
    bars = axes[0, 2].bar(acc_metrics, acc_values, color=acc_colors, alpha=0.8)
    axes[0, 2].set_ylabel('准确率 (%)')
    axes[0, 2].set_title('相对准确率指标')
    axes[0, 2].set_ylim(0, 100)
    axes[0, 2].grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, value in zip(bars, acc_values):
        axes[0, 2].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 2,
                        f'{value:.1f}%', ha='center', va='bottom', fontweight='bold')
    
    # 4. 实时数据流
    time_steps = range(len(real_values))
    axes[1, 0].plot(time_steps, real_values, 'b-', linewidth=1.5, label='真实值', alpha=0.8)
    axes[1, 0].plot(time_steps, predictions, 'r--', linewidth=1.5, label='预测值', alpha=0.8)
    axes[1, 0].set_xlabel('时间步')
    axes[1, 0].set_ylabel('位移 (μm)')
    axes[1, 0].set_title('实时数据流')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # 5. 误差时间序列
    axes[1, 1].plot(time_steps, errors, 'g-', linewidth=2, alpha=0.8)
    axes[1, 1].axhline(y=mae, color='red', linestyle='--', label=f'平均误差: {mae:.6f}μm')
    axes[1, 1].set_xlabel('时间步')
    axes[1, 1].set_ylabel('绝对误差 (μm)')
    axes[1, 1].set_title('误差时间序列')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)
    
    # 6. 处理时间分布
    axes[1, 2].hist(processing_times * 1000, bins=20, alpha=0.7, color='plum', edgecolor='black')
    axes[1, 2].axvline(x=avg_processing_time, color='red', linestyle='--', linewidth=2,
                       label=f'平均: {avg_processing_time:.2f}ms')
    axes[1, 2].set_xlabel('处理时间 (ms)')
    axes[1, 2].set_ylabel('频次')
    axes[1, 2].set_title('处理时间分布')
    axes[1, 2].legend()
    axes[1, 2].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('real_time_performance_dashboard.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("性能仪表板已保存到: real_time_performance_dashboard.png")

async def main():
    """主函数"""
    print("="*60)
    print("实时数据流处理可视化演示")
    print("="*60)
    
    # 1. 训练模型
    model, scaler = train_model_for_visualization()
    if model is None:
        return
    
    # 2. 创建实时处理器
    processor = RealTimeStreamProcessor(
        model=model,
        scaler=scaler,
        window_size=25,
        buffer_size=1000
    )

    # 关闭异常值检测以保持数据真实性
    processor.enable_outlier_detection = False
    print("已关闭异常值检测，保持数据原始性")
    
    # 3. 准备数据 - 使用后1000个点作为实时数据流
    data = pd.read_csv('v1.txt', sep='\t', encoding='utf-8')
    displacement_data = data.iloc[:, 1].values

    # 分割数据：前1000个点已用于训练，后1000个点用于实时预测
    test_data = displacement_data[1000:]  # 后1000个点
    print(f"使用后1000个数据点作为实时数据流输入")
    print(f"实时预测数据范围: 第1001-2000个数据点")

    # 使用测试数据的前25个点作为历史数据（LSTM窗口大小）
    historical_data = test_data[:25]
    processor.initialize_with_historical_data(historical_data)

    # 4. 创建数据源（从测试数据的第26个点开始）
    data_source = SimulatedDataSource('v1.txt', sampling_rate=100)
    data_source.current_index = 1025  # 1000 + 25 = 1025（跳过训练数据和历史数据）
    data_source.displacement_data = displacement_data  # 使用完整数据，但从1025开始

    # 5. 收集实时处理数据（预测975个点：1000-25=975）
    num_prediction_points = 975
    print(f"将对第1026-2000个数据点进行实时预测（共{num_prediction_points}个点）")
    real_time_data = await collect_real_time_data(processor, data_source, num_points=num_prediction_points)
    
    # 6. 生成可视化
    create_real_time_visualization(real_time_data)
    create_performance_dashboard(real_time_data)
    
    # 7. 输出统计结果
    print("\n" + "="*60)
    print("实时数据流处理统计结果")
    print("="*60)
    print("数据分割方案:")
    print("  训练数据: v1.txt 第1-1000个数据点")
    print("  测试数据: v1.txt 第1001-2000个数据点")
    print("  历史初始化: 测试数据前25个点")
    print("  实时预测: 测试数据第26-1000个点")
    print("-" * 60)
    
    real_values = np.array(real_time_data['real_values'])
    predictions = np.array(real_time_data['predictions'])
    processing_times = np.array(real_time_data['processing_times'])
    errors = np.abs(real_values - predictions)
    
    print(f"处理数据点数: {len(real_values)}")
    print(f"平均绝对误差: {np.mean(errors):.6f} μm")
    print(f"均方根误差: {np.sqrt(np.mean(errors**2)):.6f} μm")
    print(f"最大误差: {np.max(errors):.6f} μm")
    print(f"最小误差: {np.min(errors):.6f} μm")
    print(f"平均处理时间: {np.mean(processing_times)*1000:.2f} ms")
    print(f"处理速率: {1.0/np.mean(processing_times):.1f} 次/秒")
    
    # 计算准确率
    relative_errors = np.abs((real_values - predictions) / real_values) * 100
    accuracy_1percent = np.sum(relative_errors <= 1.0) / len(relative_errors) * 100
    accuracy_5percent = np.sum(relative_errors <= 5.0) / len(relative_errors) * 100
    
    print(f"±1%准确率: {accuracy_1percent:.1f}%")
    print(f"±5%准确率: {accuracy_5percent:.1f}%")
    
    print("\n可视化文件已生成:")
    print("- real_time_processing_visualization.png")
    print("- real_time_performance_dashboard.png")

if __name__ == "__main__":
    asyncio.run(main())
